<?php

namespace App\Console\Commands\Vidu;

use App\Models\PluginViduTemplate;
use App\Packages\BailianAssistant\Tools\UnderstandingTool;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TranslatePrompts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vidu:translate-prompts
                            {--dry-run : 预览翻译结果，不实际更新数据库}
                            {--force : 强制翻译所有prompt，不管是否为英文}
                            {--backup : 翻译前备份原始数据到JSON文件}
                            {--template-id= : 只翻译指定ID的模板}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '翻译Vidu模板中的英文prompt为中文';

    /**
     * 理解工具服务
     */
    protected UnderstandingTool $understandingTool;

    /**
     * 构造函数
     */
    public function __construct(UnderstandingTool $understandingTool)
    {
        parent::__construct();
        $this->understandingTool = $understandingTool;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🐾 开始翻译Vidu模板prompt...');

        // 获取要处理的模板
        $templates = $this->getTemplates();

        if ($templates->isEmpty()) {
            $this->warn('没有找到需要翻译的模板');
            return 0;
        }

        $this->info("找到 {$templates->count()} 个模板需要处理");

        // 备份数据
        if ($this->option('backup')) {
            $this->backupData($templates);
        }

        // 处理翻译
        $this->processTranslation($templates);

        $this->info('✨ 翻译完成！');
        return 0;
    }

    /**
     * 获取需要翻译的模板
     */
    protected function getTemplates()
    {
        $query = PluginViduTemplate::query()->where('status', 1);

        // 如果指定了模板ID
        if ($templateId = $this->option('template-id')) {
            $query->where('id', $templateId);
        }

        // 如果不是强制模式，只选择英文prompt
        if (!$this->option('force')) {
            $query->where(function ($q) {
                $q->whereNull('prompt_zh')
                  ->where('prompt', '!=', '')
                  ->whereNotNull('prompt');
            });
        }

        return $query->get();
    }

    /**
     * 备份原始数据
     */
    protected function backupData($templates)
    {
        $backupFile = storage_path('app/vidu_prompts_backup_' . date('Y-m-d_H-i-s') . '.json');

        $backupData = $templates->map(function ($template) {
            return [
                'id' => $template->id,
                'name' => $template->name,
                'prompt' => $template->prompt,
                'prompt_zh' => $template->prompt_zh,
            ];
        });

        file_put_contents($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->info("✅ 数据已备份到: {$backupFile}");
    }

    /**
     * 处理翻译
     */
    protected function processTranslation($templates)
    {
        $progressBar = $this->output->createProgressBar($templates->count());
        $progressBar->setFormat('详细');
        $progressBar->start();

        $successCount = 0;
        $failCount = 0;

        foreach ($templates as $template) {
            try {
                $this->translateTemplate($template);
                $successCount++;
                $progressBar->setMessage("正在翻译: {$template->name}");
            } catch (Exception $e) {
                $failCount++;
                $this->error("\n❌ 翻译失败 [{$template->id}] {$template->name}: {$e->getMessage()}");
                Log::error('Vidu模板翻译失败', [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'error' => $e->getMessage()
                ]);
            }

            $progressBar->advance();

            // 避免API频率限制
            usleep(500000); // 0.5秒延迟
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("📊 翻译统计:");
        $this->info("✅ 成功: {$successCount}");
        if ($failCount > 0) {
            $this->error("❌ 失败: {$failCount}");
        }
    }

    /**
     * 翻译单个模板
     */
    protected function translateTemplate(PluginViduTemplate $template)
    {
        $originalPrompt = $template->prompt;

        if (empty($originalPrompt)) {
            throw new Exception('prompt为空');
        }

        // 检测是否为英文
        if (!$this->option('force') && !$this->isEnglishText($originalPrompt)) {
            throw new Exception('prompt不是英文，跳过翻译');
        }

        // 翻译
        $result = $this->understandingTool->translateText($originalPrompt);

        if (!$result['status']) {
            throw new Exception($result['message'] ?? '翻译失败');
        }

        $translatedPrompt = $result['data']['output'] ?? '';

        if (empty($translatedPrompt)) {
            throw new Exception('翻译结果为空');
        }

        // 显示翻译对比
        $this->showTranslationComparison($template, $originalPrompt, $translatedPrompt);

        // 如果不是预览模式，更新数据库
        if (!$this->option('dry-run')) {
            $template->update(['prompt_zh' => $translatedPrompt]);
        }
    }

    /**
     * 检测文本是否为英文
     */
    protected function isEnglishText(string $text): bool
    {
        // 移除标点符号和空格
        $cleanText = preg_replace('/[^\p{L}\p{N}]/u', '', $text);

        if (empty($cleanText)) {
            return false;
        }

        // 计算英文字符比例
        $englishChars = preg_match_all('/[a-zA-Z]/', $cleanText);
        $totalChars = mb_strlen($cleanText);

        $englishRatio = $englishChars / $totalChars;

        // 如果英文字符比例超过60%，认为是英文
        return $englishRatio > 0.6;
    }

    /**
     * 显示翻译对比
     */
    protected function showTranslationComparison(PluginViduTemplate $template, string $original, string $translated)
    {
        if ($this->option('dry-run')) {
            $this->newLine();
            $this->info("🔍 预览模式 - 模板 [{$template->id}] {$template->name}:");
            $this->line("📝 原文: " . mb_substr($original, 0, 100) . (mb_strlen($original) > 100 ? '...' : ''));
            $this->line("🌟 译文: " . mb_substr($translated, 0, 100) . (mb_strlen($translated) > 100 ? '...' : ''));
            $this->newLine();
        }
    }
}
