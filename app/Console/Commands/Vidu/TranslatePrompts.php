<?php

namespace App\Console\Commands\Vidu;

use App\Models\PluginViduTemplate;
use App\Packages\BailianAssistant\Tools\UnderstandingTool;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TranslatePrompts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vidu:translate-prompts
                            {--dry-run : 预览翻译结果，不实际更新数据库}
                            {--force : 强制翻译所有prompt，不管是否为英文}
                            {--backup : 翻译前备份原始数据到JSON文件}
                            {--template-id= : 只翻译指定ID的模板}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '翻译Vidu模板中的英文prompt为中文';

    /**
     * 理解工具服务
     */
    protected UnderstandingTool $understandingTool;

    /**
     * 构造函数
     */
    public function __construct(UnderstandingTool $understandingTool)
    {
        parent::__construct();
        $this->understandingTool = $understandingTool;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🐾 开始翻译Vidu模板prompt...');

        // 获取要处理的模板
        $templates = $this->getTemplates();

        if ($templates->isEmpty()) {
            $this->warn('没有找到需要翻译的模板');
            return 0;
        }

        $this->info("找到 {$templates->count()} 个模板需要处理");

        // 备份数据
        if ($this->option('backup')) {
            $this->backupData($templates);
        }

        // 处理翻译
        $this->processTranslation($templates);

        $this->info('✨ 翻译完成！');
        return 0;
    }

    /**
     * 获取需要翻译的模板
     */
    protected function getTemplates()
    {
        $query = PluginViduTemplate::query();

        // 如果指定了模板ID
        if ($templateId = $this->option('template-id')) {
            $query->where('id', $templateId);
        }

        // 如果不是强制模式，只选择英文prompt
        if (! $this->option('force')) {
            $query->where(function ($q) {
                $q->whereNull('prompt_zh')
                    ->where('prompt', '!=', '')
                    ->whereNotNull('prompt');
            });
        }

        return $query->get();
    }

    /**
     * 备份原始数据
     */
    protected function backupData($templates)
    {
        $backupFile = storage_path('app/vidu_prompts_backup_'.date('Y-m-d_H-i-s').'.json');

        $backupData = $templates->map(function ($template) {
            return [
                'id'        => $template->id,
                'name'      => $template->name,
                'prompt'    => $template->prompt,
                'prompt_zh' => $template->prompt_zh,
            ];
        });

        file_put_contents($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->info("✅ 数据已备份到: {$backupFile}");
    }

    /**
     * 处理翻译
     */
    protected function processTranslation($templates)
    {
        $totalCount = $templates->count();
        $successCount = 0;
        $failCount = 0;
        $skipCount = 0;
        $startTime = time();

        // 创建增强的进度条
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->setFormat(
            " %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%\n" .
            " 🐾 <fg=cyan>%message%</fg=cyan>\n" .
            " 📊 <fg=green>成功: %success%</fg=green> | <fg=red>失败: %failed%</fg=red> | <fg=yellow>跳过: %skipped%</fg=yellow>"
        );

        // 初始化进度条消息
        $progressBar->setMessage('准备开始翻译...', 'message');
        $progressBar->setMessage('0', 'success');
        $progressBar->setMessage('0', 'failed');
        $progressBar->setMessage('0', 'skipped');
        $progressBar->start();

        foreach ($templates as $index => $template) {
            $currentNum = $index + 1;

            // 更新当前状态
            $progressBar->setMessage("正在翻译第 {$currentNum}/{$totalCount} 个: {$template->name}", 'message');
            $progressBar->display();

            try {
                $this->translateTemplate($template);
                $successCount++;

                // 显示成功状态
                $this->line("\n<fg=green>✅ [{$template->id}] {$template->name} - 翻译成功</fg=green>");

            } catch (Exception $e) {
                // 检查是否是跳过的情况
                if (str_contains($e->getMessage(), '跳过翻译') || str_contains($e->getMessage(), 'prompt不是英文')) {
                    $skipCount++;
                    $this->line("\n<fg=yellow>⏭️  [{$template->id}] {$template->name} - {$e->getMessage()}</fg=yellow>");
                } else {
                    $failCount++;
                    $this->line("\n<fg=red>❌ [{$template->id}] {$template->name} - {$e->getMessage()}</fg=red>");

                    Log::error('Vidu模板翻译失败', [
                        'template_id'   => $template->id,
                        'template_name' => $template->name,
                        'error'         => $e->getMessage()
                    ]);
                }
            }

            // 更新统计信息
            $progressBar->setMessage((string)$successCount, 'success');
            $progressBar->setMessage((string)$failCount, 'failed');
            $progressBar->setMessage((string)$skipCount, 'skipped');

            $progressBar->advance();

            // 避免API频率限制
            if ($currentNum < $totalCount) {
                $progressBar->setMessage('等待中... (避免API频率限制)', 'message');
                $progressBar->display();
                usleep(500000); // 0.5秒延迟
            }
        }

        $progressBar->setMessage('翻译完成！', 'message');
        $progressBar->finish();
        $this->newLine(2);

        // 显示最终统计
        $this->displayFinalStatistics($totalCount, $successCount, $failCount, $skipCount, $startTime);
    }

    /**
     * 翻译单个模板
     */
    protected function translateTemplate(PluginViduTemplate $template)
    {
        $originalPrompt = $template->prompt;

        if (empty($originalPrompt)) {
            throw new Exception('prompt为空，跳过翻译');
        }

        // 如果已有中文翻译且不是强制模式，跳过
        if (!$this->option('force') && !empty($template->prompt_zh)) {
            throw new Exception('已有中文翻译，跳过翻译');
        }

        // 检测是否为英文
        if (! $this->option('force') && ! $this->isEnglishText($originalPrompt)) {
            throw new Exception('prompt不是英文，跳过翻译');
        }

        // 翻译
        $result = $this->understandingTool->translateText($originalPrompt);

        if (! $result['status']) {
            throw new Exception($result['message'] ?? '翻译API调用失败');
        }

        $output = $result['data']['output'] ?? '';
        $output = json_decode($output, true);

        if (empty($output) || !isset($output['translation'])) {
            throw new Exception('翻译结果解析失败');
        }

        $translatedPrompt = $output['translation'];

        if (empty($translatedPrompt)) {
            throw new Exception('翻译结果为空');
        }

        // 显示翻译对比
        $this->showTranslationComparison($template, $originalPrompt, $translatedPrompt);

        // 如果不是预览模式，更新数据库
        if (! $this->option('dry-run')) {
            $template->update(['prompt_zh' => $translatedPrompt]);
        }
    }

    /**
     * 检测文本是否为英文
     */
    protected function isEnglishText(string $text): bool
    {
        // 移除标点符号和空格
        $cleanText = preg_replace('/[^\p{L}\p{N}]/u', '', $text);

        if (empty($cleanText)) {
            return false;
        }

        // 计算英文字符比例
        $englishChars = preg_match_all('/[a-zA-Z]/', $cleanText);
        $totalChars   = mb_strlen($cleanText);

        $englishRatio = $englishChars / $totalChars;

        // 如果英文字符比例超过60%，认为是英文
        return $englishRatio > 0.6;
    }

    /**
     * 显示最终统计信息
     */
    protected function displayFinalStatistics(int $total, int $success, int $failed, int $skipped, int $startTime)
    {
        $endTime = time();
        $duration = $endTime - $startTime;
        $minutes = floor($duration / 60);
        $seconds = $duration % 60;

        $this->info("🎉 翻译任务完成！");
        $this->newLine();

        // 创建统计表格
        $headers = ['项目', '数量', '百分比'];
        $rows = [
            ['<fg=blue>总计</fg=blue>', $total, '100%'],
            ['<fg=green>成功</fg=green>', $success, $total > 0 ? round(($success / $total) * 100, 1) . '%' : '0%'],
            ['<fg=red>失败</fg=red>', $failed, $total > 0 ? round(($failed / $total) * 100, 1) . '%' : '0%'],
            ['<fg=yellow>跳过</fg=yellow>', $skipped, $total > 0 ? round(($skipped / $total) * 100, 1) . '%' : '0%'],
        ];

        $this->table($headers, $rows);

        // 显示时间信息
        $this->info("⏱️  总耗时: {$minutes}分{$seconds}秒");

        if ($success > 0) {
            $avgTime = round($duration / $success, 1);
            $this->info("📈 平均每个成功翻译耗时: {$avgTime}秒");
        }

        // 显示建议
        if ($failed > 0) {
            $this->warn("💡 建议: 检查日志文件查看失败详情，可能需要重新运行失败的项目");
        }

        if ($skipped > 0) {
            $this->info("💡 提示: 跳过的项目通常是因为已有中文翻译或不是英文内容");
        }
    }

    /**
     * 显示翻译对比
     */
    protected function showTranslationComparison(PluginViduTemplate $template, string $original, string $translated)
    {
        if ($this->option('dry-run')) {
            $this->newLine();
            $this->info("🔍 预览模式 - 模板 [{$template->id}] {$template->name}:");
            $this->line("📝 原文: ".mb_substr($original, 0, 100).(mb_strlen($original) > 100 ? '...' : ''));
            $this->line("🌟 译文: ".mb_substr($translated, 0, 100).(mb_strlen($translated) > 100 ? '...' : ''));
            $this->newLine();
        }
    }
}
